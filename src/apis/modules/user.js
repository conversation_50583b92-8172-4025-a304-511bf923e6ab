import request from "@/utils/request";

export default {
    login: (data) => request.get(`/sso-center-api/account/auth/parentPublicLogin?code=${ data.code }&state=${ data.state }`),
    logout: () => request.post('/sso-center-api/account/logout'),
    getLoginStatus: () => request.post('/sso-center-api/account/checkLoginStatus'),
		getWxConfig: () => request.get('/sso-center-api/account/auth/parentConfig'),
		// 冀时办登录
		jsbLogin: token => request.get('/sso-center-api/oauth2/loginByJsb?token=' + token),
		// 省平台登录
		provPltFmLogin: token => request.get('/sso-center-api/oauth2/login?token=' + token)
}