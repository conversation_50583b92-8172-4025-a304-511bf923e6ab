const modulesFiles = require.context('./modules', true, /\.js$/)

const modules = modulesFiles.keys().reduce((modules, modulePath) => {
    const moduleName = modulePath.replace(/^\.\/(.*)\.\w+$/, '$1');
    const value = require.context('./modules', true, /\.js$/)(modulePath);
    modules[moduleName] = value.default;
    return modules;
}, {})


const httpRequest = (url, method) => {
    return new Promise((resolve, reject) => {
        var xhr = new XMLHttpRequest();
        xhr.open(method, url, true);
        xhr.onreadystatechange = () => {
            if (xhr.readyState === 4 && xhr.status === 200) {
                resolve(xhr.responseText);
            } else if (xhr.readyState === 4 && xhr.status !== 200) {
                reject(xhr.responseText);
            }
        };
        xhr.send();
    })
}


export default {

    async install(Vue) {
        let url = process.env.VUE_APP_BASE_API + '/sso-center-api/securityStatus';
        const dev = process.env.NODE_ENV == 'development'
        // #ifdef MP-WEIXIN
        url = dev ? `${process.env.VUE_APP_BASE_API_HOST}/sso-center-api/securityStatus`
            : `${process.env.VUE_APP_BASE_API_HOST}/${process.env.VUE_APP_BASE_API}/sso-center-api/securityStatus`;
        // #endif
        /* uni.request({
            url: url,
            method: 'POST',
            success: function ({ data }) {
                uni.setStorageSync('securityStatus', data.data)
            },
            fail: function (err) {
                console.error(err)
            }
        }) */

        // // 请求接口是否加密
        // const data = await httpRequest(process.env.VUE_APP_BASE_API + '/sso-center-api/securityStatus', 'POST')
        // uni.setStorageSync('securityStatus', JSON.parse(data).data)
        // this.$apis
        Vue.prototype.$apis = modules
        // this.$u.apis
        Vue.prototype.$u.apis = modules;
    }
}