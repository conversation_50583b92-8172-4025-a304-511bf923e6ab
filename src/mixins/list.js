// 带翻页判断的列表
let List = {
	data() {
		return {
			search: {
				pageNumber: 1,
				pageSize: 15
			},
			mainList: {
				list: [],
				// 总数
				total: 0,
				// 已加载数
				alLoad: 0,
				// 加载状态
				status: 'loadmore',
				loadTxt: {
					loadmore: '点此加载更多',
					loading: '加载中...',
					nomore: '没有更多了'
				},
			}
		}
	},
	// 刷新
	onPullDownRefresh() {
		this.resetQry()
	},
	// 触底
	onReachBottom() {
		this.nextPage()
	},
	methods: {
		// 重置查询
		resetQry() {
			this.mainList.list = []
			this.mainList.total = 0
			this.mainList.alLoad = 0
			this.search.pageNumber = 1
			this.getList()
		},
		// 翻页
		nextPage() {
			if (this.mainList.alLoad != this.mainList.total) {
				// 翻页
				this.search.pageNumber++
				this.mainList.status = 'loading'
				this.getList()
			}
		},
		// 请求列表
		getList() {
			console.log('请求列表')
		}
	}
}

export default List