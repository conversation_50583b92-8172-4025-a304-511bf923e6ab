
/* start--文本行数限制--start */
.sd-line-1 {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.sd-line-2 {
    -webkit-line-clamp: 2;
}

.sd-line-3 {
    -webkit-line-clamp: 3;
}

.sd-line-4 {
    -webkit-line-clamp: 4;
}

.sd-line-5 {
    -webkit-line-clamp: 5;
}

.sd-line-2, .sd-line-3, .sd-line-4, .sd-line-5 {
    overflow: hidden;
	word-break: break-all;
    text-overflow: ellipsis; 
    display: -webkit-box; // 弹性伸缩盒
    -webkit-box-orient: vertical; // 设置伸缩盒子元素排列方式
}

/* end--文本行数限制--end */


/* start--flex布局--start */
.sd-flex {
	/* #ifndef APP-NVUE */
	display: flex;
	/* #endif */
	flex-direction: row;
	align-items: center;
}

.sd-flex-wrap {
	flex-wrap: wrap;
}

.sd-flex-nowrap {
	flex-wrap: nowrap;
}

.sd-col-center {
	align-items: center;
}

.sd-col-top {
	align-items: flex-start;
}

.sd-col-bottom {
	align-items: flex-end;
}

.sd-row-center {
	justify-content: center;
}

.sd-row-left {
	justify-content: flex-start;
}

.sd-row-right {
	justify-content: flex-end;
}

.sd-row-between {
	justify-content: space-between;
}

.sd-row-around {
	justify-content: space-around;
}

.sd-text-left {
	text-align: left;
}

.sd-text-center {
	text-align: center;
}

.sd-text-right {
	text-align: right;
}

.sd-flex-col {
	/* #ifndef APP-NVUE */
	display: flex;
	/* #endif */
	flex-direction: column;
}

// 定义flex等分
@for $i from 0 through 12 {
	.sd-flex-#{$i} {
		flex: $i;
	}
}

/* end--flex布局--end */

// 定义字体(px)单位，小于20都为px单位字体
@for $i from 9 to 20 {
	.sd-font-#{$i} {
		font-size: $i + px;
	}
}

// 定义字体(rpx)单位，大于或等于20的都为rpx单位字体
@for $i from 20 through 40 {
	.sd-font-#{$i} {
		font-size: $i + rpx;
	}
}

// 定义内外边距，历遍1-80
@for $i from 0 through 80 {
	// 只要双数和能被5除尽的数
	@if $i % 2 == 0 or $i % 5 == 0 {
		// 得出：u-margin-30或者u-m-30
		.sd-margin-#{$i}, .sd-m-#{$i} {
			margin: $i + rpx!important;
		}
		
		// 得出：u-padding-30或者u-p-30
		.sd-padding-#{$i}, .sd-p-#{$i} {
			padding: $i + rpx!important;
		}
		
		@each $short, $long in l left, t top, r right, b bottom {
			// 缩写版，结果如： u-m-l-30
			// 定义外边距
			.sd-m-#{$short}-#{$i} {
				margin-#{$long}: $i + rpx!important;
			}
			
			// 定义内边距
			.sd-p-#{$short}-#{$i} {
				padding-#{$long}: $i + rpx!important;
			}
			
			// 完整版，结果如：u-margin-left-30
			// 定义外边距
			.sd-margin-#{$long}-#{$i} {
				margin-#{$long}: $i + rpx!important;
			}
			
			// 定义内边距
			.sd-padding-#{$long}-#{$i} {
				padding-#{$long}: $i + rpx!important;
			}
		}
	}
}

// 重置nvue的默认关于flex的样式
.sd-reset-nvue {
	flex-direction: row;
	align-items: center;
}


/* start 定位相关***/
.sd-relative,
.sd-rela {
	position: relative;
}

.sd-absolute,
.sd-abso {
	position: absolute;
}

/* end 定位相关***/

/* 公共组件：模拟select */
.normal-select {
	width: 100%;
	.fake-input {
		position: relative;
		width: 100%;
		line-height: 38px;
		padding-left: 9px;
		box-sizing: border-box;
		border: 0.5px solid #dadbde;
		border-radius: 4px;
		.res-txt {
			font-size: 14px;
			max-width: calc(100% - 24px);
		}
		.ph-txt {
			color: #c0c4cc;
		}
		.fake-input-icon {
			position: absolute;
			top: 50%;
			right: 10px;
			transform: translate(0, -50%);
		}
	}
	/* 自定义弹窗select */
	.selector-wrap {
		height: 60vh;
		background-color: #f1f1f1;
		.selector-title {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 10px 0;
			border-bottom: 0.5px solid #dadbde;
			.s-t-btn {
				font-size: 15px;
				padding: 0 15px;
			}
			.s-t-cancel {
				color: #909193;
			}
			.s-t-confirm {
				color: #3c9cff;
			}
		}
		
		.selector-list {
			max-height: calc(60vh - 61px);
			padding: 10px 20px;
			overflow: auto;
			&::after {
				content: '';
				display: block;
				height: 20px;
				width: 0;
			}
		}
		.selector-item {
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-bottom: 10px;
			padding: 10px;
			background-color: #FFF;
			border-radius: 6px;
		}
		.item-selected {
			background-color: #3c9cff;
			color: #FFF;
		}
		.res-txt {
			flex: 0 0 calc(100% - 50px);
		}
		.select-icon {
			flex: 0 0 20px;
			text-align: center;
		}
	}
}

.normal-img-upload {
	.upload-succ-list {
		
	}
	.upload-succ-item {
		position: relative;
		display: flex;
		justify-content: center;
		align-items: center;
		width: 80px;
		height: 80px;
		background-color: #f4f5f7;
		& > image {
			width: 100%;
			height: 100%;
		}
	}
	.del-btn {
		position: absolute;
		top: 0;
		right: 0;
		z-index: 1;
		width: 35rpx;
		line-height: 35rpx;
		font-size: 24rpx;
		text-align: center;
		color: #fff;
		background-color: #f5222d;
	}
	.sample-img {
		margin-top: 10px;
	}
}

.normal-img-ex {
	.ph {
		width: 80px;
		line-height: 80px;
		font-size: 13px;
		text-align: center;
		background-color: #f3f4f6;
	}
}

/* 覆盖样式 */
.u-form-item__body__left__content__required {
	left: 0 !important;
}
.u-form-item__body__left__content__required + .u-form-item__body__left__content__label {
	text-indent: 11px;
}

.entrance {
	.submit-btn {
		margin: 20px 0;
	}
}

/* 报名页 */
.entrance {
	.submit-action {
		margin-top: 20px;
	}
}