<template>
	<div class="img-comp-wrap img-item-wrap">
		<span v-if="val" class="img-del-btn" @click="delImg">×</span>
		<!-- 已经上传了图 -->
		<normal-img-ex v-show="val" :item-config.sync="itemCfg"></normal-img-ex>
		<!-- 没有上传 -->
		<normal-img-upload v-show="!val" ref="uploader" :item-config.sync="itemCfg" @value-change="uploadValueChange"></normal-img-upload>
	</div>
</template>

<script>
import NormalImgUpload from "@/components/Form/NormalImgUpload"
import NormalImgEx from "@/components/Exhibition/NormalImgEx"
export default {
	components: {
		NormalImgUpload,
		NormalImgEx
	},
	data() {
		return {
			val: '',
			itemCfg: {}
		}
	},
	props: {
		// 字段所有配置
		itemConfig: {
			type: Object,
			required: true
		}
	},
	watch: {
		// itemConfig.fieldValue
		itemConfig: {
			handler(newV, oldV) {
				// console.log(newV, oldV)
				this.itemCfg = this.itemConfig
				if (newV.fieldValue && newV.fieldValue != oldV.fieldValue) {
					this.val = this.itemConfig.fieldValue
				}
			},
			immediate: true,
			deep: true
		}
	},
	created() {
		if (this.itemConfig) {
			this.itemCfg = this.itemConfig
		}
	},
	methods: {
		// 删除
		delImg() {
			// 调用上传组件的删除
			this.$refs['uploader'].removePic({
				response: {
					data: this.val
				}
			}, [])
		},
		// 值改变
		uploadValueChange(v) {
			console.log('img包裹组件里', v)
			this.val = v.val
			this.itemCfg.fieldValue = v.val
			this.$emit("value-change", v)
		}
	}
}
</script>

<style>
</style>