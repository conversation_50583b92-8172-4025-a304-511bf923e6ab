<template>
	<view class="normal-img-ex">
		<u-image
			width="80"
			height="80"
			v-if="modelData"
			:src="modelData" 
			:preview-src-list="previewList"
			@click="preview"
		>
		</u-image>
		<view class="ph" v-else>暂无图片</view>
	</view>
</template>

<script>
import { imgPrefix } from '@/utils/common'
export default {
	name: 'normal-img-ex',
	data() {
		return {
			host: this.$store.state.appHost,
			modelData: '',
			previewList: []
		}
	},
	props: {
		// 字段所有配置
		itemConfig: {
			type: Object,
			required: true
		}
	},
	/* created() {
		if (this.itemConfig.fieldValue) {
			this.modelData = `${ this.host }${ imgPrefix() }${ this.itemConfig.fieldValue }`
			this.previewList = [this.modelData]
		}
	}, */
	watch: {
		'itemConfig.fieldValue': {
			handler(newV, oldV) {
				if (newV && newV != oldV) {
					this.modelData = `${ this.host }${ imgPrefix() }${ this.itemConfig.fieldValue }`
					this.previewList = [this.modelData]
				}
			},
			immediate: true
		}
	},
	methods: {
		preview() {
			let that = this
			uni.previewImage({
				current: 0,
				urls: that.previewList
			})
		}
	}
}
</script>
