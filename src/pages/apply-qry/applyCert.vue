<template>
	<view class="apply-cert">
		<rich-text :nodes="content"></rich-text>
		<view class="school-nm">{{ schoolName }}</view>
	</view>
</template>

<script>
import { notifyDetail } from "@/apis/admission.js"
export default {
	data() {
		return {
			key: this.$store.state.applyQryResult.studentId,
			content: '',
			schoolName: ''
		}
	},
	created() {
		this.getData()
	},
	methods: {
		getData() {
			notifyDetail({
				key: this.key
			}).then(res => {
				this.content = res.content
				this.schoolName = res.schoolName
			})
		}
	}
}
</script>

<style scoped lang="scss">
.apply-cert {
	padding: 10px;
	.school-nm {
		text-align: right;
	}
}
</style>