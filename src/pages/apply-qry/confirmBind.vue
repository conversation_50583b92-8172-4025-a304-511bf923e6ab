<template>
	<view class="confirm-bind">
		<view class="stu-info">学生姓名：{{ nm }}</view>
		<view class="stu-info">学生身份证号：{{ idCard }}</view>
		<u-alert title="注：点击【确认】按钮以后信息将无法进行修改，请仔细核对信息，一个公众号只能查询一个学生身份证号，确认信息无误以后，点击【确认】按钮，查询学生信息" type="error"></u-alert>
		<view class="c-b-actions">
			<view class="btn-wrap">
				<u-button type="info" text="取消" @click="_goBack()"></u-button>
			</view>
			<view class="btn-wrap">
				<u-button type="primary" text="确认" @click="confirmBinding"></u-button>
			</view>
		</view>
		<u-toast ref="successTips"></u-toast>
	</view>
</template>

<script>
import { bindIdCard2CurWx } from '@/apis/admission'
export default {
	data() {
		return {
			nm: '',
			idCard: '',
			stuId: ''
		}
	},
	onLoad() {
		let that = this
		// #ifdef APP-NVUE
		const evtChanel = that.$scope.eventChannel
		// #endif
		// #ifndef APP-NVUE
		const evtChanel = that.getOpenerEventChannel()
		// #endif
		evtChanel.on('acceptDataFromOpenerPage', function (data) {
			that.nm = data.nm
			that.idCard = data.idCard
			that.stuId = data.stuId
		})
	},
	methods: {
		// 确认绑定
		confirmBinding() {
			bindIdCard2CurWx({
				userId: this.$store.state.userId,
				studentId: this.stuId
			}).then(res => {
				this.$refs.successTips.show({
					message: '绑定成功',
					type: 'success',
					duration: 3000,
					complete() {
						uni.navigateBack({
							delta: 1
						})
					}
				})
			})
		}
	}
}
</script>

<style scoped lang="scss">
.confirm-bind {
	height: 100vh;
	padding: 10px;
	box-sizing: border-box;
	font-size: 14px;
	background-color: #FFF;
	.stu-info {
		margin-bottom: 10px;
	}
	.c-b-actions {
		position: fixed;
		bottom: 20px;
		left: 0;
		width: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
		.btn-wrap {
			flex: 0 0 30%;
			margin: 0 5%;
		}
	}
}
</style>