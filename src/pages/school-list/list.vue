<template>
	<view class="school-list">
		<template v-if="!wish.show">
<!--			<u-search shape="square" placeholder="请输入学校名称" v-model="search.keywords" @change="debounceQry" :showAction="false"></u-search>-->
		</template>
		<template v-if="wish.show">
			<u-form :model="wish.form" ref="wish" labelPosition="top" errorType="toast" labelWidth="auto">
				<u-form-item prop="vol1" label="第一志愿">
					<view class="normal-select fixed-type wish-tp">
						<u-action-sheet :actions="mainList.list" title="第一志愿" :show="wish.actualWishMap[0].show" @select="changeWish($event, 0)" @close="closeSelector(0)" :closeOnClickOverlay="false"></u-action-sheet>
						<view class="tp-select">
							<view class="fake-input" @click="openSelector(0)">
								<view class="res-txt" v-if="wish.form.vol1">{{ wish.actualWishMap[0].volunteerSchoolName }}</view>
								<view class="ph-txt" v-else>请选择第一志愿</view>
								<text class="fake-input-icon">
									<u-icon name="arrow-down" color="#dadbde" size="14px"></u-icon>
								</text>
							</view>
							<view class="tp-actions">
								<u-button type="primary" :disabled="!wish.form.vol1" @click="clearWish(0)">清空</u-button>
							</view>
						</view>
					</view>
				</u-form-item>
				<u-form-item prop="vol2" label="第二志愿">
					<view class="normal-select fixed-type wish-tp">
						<u-action-sheet :actions="mainList.list" title="第二志愿" :show="wish.actualWishMap[1].show" @select="changeWish($event, 1)" @close="closeSelector(1)" :closeOnClickOverlay="false"></u-action-sheet>
						<view class="tp-select">
							<view class="fake-input" @click="openSelector(1)">
								<view class="res-txt" v-if="wish.form.vol2">{{ wish.actualWishMap[1].volunteerSchoolName }}</view>
								<view class="ph-txt" v-else>请选择第二志愿</view>
								<text class="fake-input-icon">
									<u-icon name="arrow-down" color="#dadbde" size="14px"></u-icon>
								</text>
							</view>
							<view class="tp-actions">
								<u-button type="primary" :disabled="!wish.form.vol2" @click="clearWish(1)">清空</u-button>
							</view>
						</view>
					</view>
				</u-form-item>
		<u-form-item prop="vol3" label="第三志愿">
					<view class="normal-select fixed-type wish-tp">
						<u-action-sheet :actions="mainList.list" title="第三志愿" :show="wish.actualWishMap[2].show" @select="changeWish($event, 2)" @close="closeSelector(2)" :closeOnClickOverlay="false"></u-action-sheet>
						<view class="tp-select">
							<view class="fake-input" @click="openSelector(2)">
								<view class="res-txt" v-if="wish.form.vol3">{{ wish.actualWishMap[2].volunteerSchoolName }}</view>
								<view class="ph-txt" v-else>请选择第三志愿</view>
								<text class="fake-input-icon">
									<u-icon name="arrow-down" color="#dadbde" size="14px"></u-icon>
								</text>
							</view>
							<view class="tp-actions">
								<u-button type="primary" :disabled="!wish.form.vol3" @click="clearWish(2)">清空</u-button>
							</view>
						</view>
					</view>
				</u-form-item>
        <!--
				<u-form-item prop="vol4" label="第四志愿">
					<view class="normal-select fixed-type wish-tp">
						<u-action-sheet :actions="mainList.list" title="第四志愿" :show="wish.actualWishMap[3].show" @select="changeWish($event, 3)" @close="closeSelector(3)" :closeOnClickOverlay="false"></u-action-sheet>
						<view class="tp-select">
							<view class="fake-input" @click="openSelector(3)">
								<view class="res-txt" v-if="wish.form.vol4">{{ wish.actualWishMap[3].volunteerSchoolName }}</view>
								<view class="ph-txt" v-else>请选择第四志愿</view>
								<text class="fake-input-icon">
									<u-icon name="arrow-down" color="#dadbde" size="14px"></u-icon>
								</text>
							</view>
							<view class="tp-actions">
								<u-button type="primary" :disabled="!wish.form.vol4" @click="clearWish(3)">清空</u-button>
							</view>
						</view>
					</view>
				</u-form-item>
				<u-form-item prop="vol5" label="第五志愿">
					<view class="normal-select fixed-type wish-tp">
						<u-action-sheet :actions="mainList.list" title="第五志愿" :show="wish.actualWishMap[4].show" @select="changeWish($event, 4)" @close="closeSelector(4)" :closeOnClickOverlay="false"></u-action-sheet>
						<view class="tp-select">
							<view class="fake-input" @click="openSelector(4)">
								<view class="res-txt" v-if="wish.form.vol5">{{ wish.actualWishMap[4].volunteerSchoolName }}</view>
								<view class="ph-txt" v-else>请选择第五志愿</view>
								<text class="fake-input-icon">
									<u-icon name="arrow-down" color="#dadbde" size="14px"></u-icon>
								</text>
							</view>
							<view class="tp-actions">
								<u-button type="primary" :disabled="!wish.form.vol5" @click="clearWish(4)">清空</u-button>
							</view>
						</view>
					</view>
				</u-form-item>-->
			</u-form>
		</template>
		<u-list @scrolltolower="nextPage" class="s-list" v-if="mainList.list.length > 0">
			<u-list-item v-for="(item, idx) in mainList.list" :key="idx" class="s-item">
				<view class="s-i-name">{{ item.deptName }}</view>
				<view class="s-i-actions" :class="{ 'show-wish': wish.show }">
<!--					<view class="wish-apply-count" v-if="wish.show">第一志愿已报{{ item._firstWishCount }}人</view>-->
					<view class="action-btn detail-btn" @click="seeDetail(item)" :class="{ 'ad-btn': wish.show }">查看学校风采</view>
					<view class="action-btn ad-btn" @click="go2AdForm(item)" v-if="!wish.show">报名</view>
				</view>
			</u-list-item>
		</u-list>
		<u-empty mode="list" text="暂无学校" v-else></u-empty>
		<u-modal :show="rejectSchool.show" :title="rejectSchool.title" :content='rejectSchool.content'></u-modal>
		<u-toast ref="uToast"></u-toast>
		<view class="s-i-actions show-wish" v-if="wish.show">
			<u-button class="action-btn" type="primary" @click="go2WishAdForm" text="报名"></u-button>
		</view>
	</view>
</template>

<script>
import { schoolListByEntry, schoolWishTotal } from "@/apis/admission.js"
import List from '@/mixins/list.js'
import { wishRequiredSetUpIds } from '@/utils/dictionary.js'
export default {
	mixins: [List],
	data() {
		return {
			search: {
			  keywords: "",
				// 乡镇/城区
			  nature: this.$store.state.entry1 - 1,
				// 学段
			  period: "",
				deptCode: this.$store.state.deptCode,
			  type: 1,
				rejectSchoolId: '',
				setUpId: this.$store.state.entry3
			},
			numberCn: ['一', '二', '三', '四', '五', '六'],
			wish: {
				// 页面展示表单
				form: {
					vol1: '',
					vol2: '',
					vol3: '',
					// vol4: '',
					// vol5: ''
				},
				// 实际后端需要的结构
				actualWishMap: [{
					severalVolunteer: 1,
					volunteerSchoolId: '',
					volunteerSchoolName: '',
					show: false
				}, {
					severalVolunteer: 2,
					volunteerSchoolId: '',
					volunteerSchoolName: '',
					show: false
				}
        , {
					severalVolunteer: 3,
					volunteerSchoolId: '',
					volunteerSchoolName: '',
					show: false
				}
        // , {
				// 	severalVolunteer: 4,
				// 	volunteerSchoolId: '',
				// 	volunteerSchoolName: '',
				// 	show: false
				// }, {
				// 	severalVolunteer: 5,
				// 	volunteerSchoolId: '',
				// 	volunteerSchoolName: '',
				// 	show: false
				// }
        ],
				show: false
			},
			// 已被学校驳回
			rejectSchool: {
				show: false,
				title: '提示',
				content: '已被当前学校驳回报名，请选择其它学校'
			}
		}
	},
	async created() {
		this.$store.commit('SET_SCHOOL_DETAIL', {})
		this.$store.commit('setWish', [])
		let entry2 = this.$store.state.entry2
		// 城区幼儿园
		if (entry2 == 62) {
			this.search.period = 1
		} else if (entry2 == 7 || entry2 == 63) {
			// 7乡镇小学，63城区小学
			this.search.period = 2
		} else if (entry2 == 8 || entry2 == 64) {
			// 8乡镇初中，64城区初中
			this.search.period = 3
		}
		if (wishRequiredSetUpIds.indexOf(this.$store.state.entry3) != -1) {
			this.wish.show = true
			// this.getWishTotal()
		}
		this.getList()
	},
	methods: {
		// 获取列表
		getList() {
			schoolListByEntry(this.search).then(res => {
				let newPageData = res.records
				newPageData.forEach(v => {
					v.name = v.deptName
				})
				if (this.wish.show) {
					schoolWishTotal({}).then(res => {
						res.forEach(v1 => {
							newPageData.forEach(v2 => {
								if (v1.volunteerSchoolId == v2.id)
								v2._firstWishCount = v1.volunteerCount
							})
						})
						this.mainList.list = this.mainList.list.concat(newPageData)
					})
				} else {
					this.mainList.list = this.mainList.list.concat(newPageData)
				}
				// 不足一页
				if (newPageData.length < this.search.pageSize) {
					this.mainList.alLoad = this.mainList.total
					this.mainList.status = 'nomore'
				} else {
					this.mainList.alLoad = this.mainList.list.length
				}
				uni.stopPullDownRefresh()
			})
		},
		// 志愿人数
		getWishTotal() {

		},
		// 输入框change防抖
		debounceQry() {
			this.mainList.list = []
			uni.$u.debounce(this.resetQry, 800)
		},
		// 查看学校风采
		seeDetail(item) {
			this.$store.commit('SET_SCHOOL_DETAIL', item)
			uni.navigateTo({
				url: '/pages/school-list/detail'
			})
		},
		// 开启志愿选择
		openSelector(idx) {
			this.wish.actualWishMap[idx].show = true
		},
		// 关闭志愿选择
		closeSelector(idx) {
			this.wish.actualWishMap[idx].show = false
		},
		// 清空志愿
		clearWish(idx) {
			this.wish.form[`vol${ idx + 1 }`] = ''
			this.wish.actualWishMap[idx].volunteerSchoolId = ''
			this.wish.actualWishMap[idx].volunteerSchoolName = ''
		},
		// 修改志愿
		changeWish(evt, idx) {
			let curWish = evt.id
			// 先赋值
			this.wish.form[`vol${ idx + 1 }`] = curWish
			this.wish.actualWishMap[idx].volunteerSchoolId = curWish
			this.wish.actualWishMap[idx].volunteerSchoolName = this.mainList.list.find(v => v.id == curWish).deptName
			let allWish = Object.values(this.wish.form)
			let isConflict = allWish.filter(v => v && v == curWish)
			// 同一个学校: id为1810512784115929090和1810512958510895105
			let sameSchool = ['1810512784115929090', '1810512958510895105']
			// 检查是否同一个学校
			let isSameSchool = allWish.filter(v => sameSchool.indexOf(v) != -1)
			// 重复时再清空
			if (isConflict.length > 1) {
				this.wish.form[`vol${ idx + 1 }`] = ''
				this.wish.actualWishMap[idx].volunteerSchoolId = ''
				this.wish.actualWishMap[idx].volunteerSchoolName = ''
				this.$refs.uToast.show({
					type: 'error',
					message: '志愿学校不能重复'
				})
			} else if (isSameSchool.length > 1) {
				this.wish.form[`vol${ idx + 1 }`] = ''
				this.wish.actualWishMap[idx].volunteerSchoolId = ''
				this.wish.actualWishMap[idx].volunteerSchoolName = ''
				this.$refs.uToast.show({
					type: 'error',
					message: '走读学校和寄宿学校只能二选一'
				})
			}
		},
		// 选好志愿，前往报名
		go2WishAdForm() {
			// 所有未选择志愿的行数
			let emptyWish = []
			this.wish.actualWishMap.forEach((v, i) => {
				if (!v.volunteerSchoolName) {
					emptyWish.push(this.numberCn[i])
				}
			})
			if (emptyWish.length > 0) {
				this.$refs.uToast.show({
					type: 'error',
					message: `请选择第${ emptyWish.join('、') }志愿`
				})
			} else {
				this.$store.commit('setWish', this.wish.actualWishMap)
				uni.navigateTo({
					url: `/pages/ad-form/index`
				})
			}
		},
		// 前往报名
		go2AdForm(item) {
			this.$store.commit('SET_SCHOOL_DETAIL', item)
			uni.navigateTo({
				url: `/pages/ad-form/index`
			})
		}
	}
}
</script>

<style scoped lang="scss">
.school-list {
	padding: 10px;
	.s-list {
		padding: 15px 0;
	}
	.s-item {
		padding: 10px;
		padding-bottom: 0;
		margin-bottom: 10px;
		border: 1px solid #dcdfe6;
		background-color: #FFF;
		border-radius: 4px;
	}
	.s-i-name {
		text-align: center;
		margin-bottom: 10px;
	}
	.s-i-actions {
		display: flex;
		justify-content: space-between;
		align-items: center;
		text-align: center;
		.action-btn {
			flex: 0 0 30%;
			padding: 10px;
			font-size: 14px;
		}
		.ad-btn {
			color: #5ac725;
		}
	}
	.show-wish {
		position: relative;
		justify-content: center;
		.wish-apply-count {
			color: #888;
			font-size: 14px;
		}
	}
	.wish-tp {
		.tp-select {
			display: flex;
			justify-content: space-between;
			align-items: center;
		}
		.fake-input {
			flex: 0 0 80%;
		}
		.tp-actions {
			flex: 0 0 18%;
		}
	}
}
</style>