<template>
	<view class="content">
		<view class="banner">
			<image src="/static/home-banner-bg.png" class="banner-bg"></image>
			<view class="cur-area" @click="go2AreaSelect">{{ curArea }}</view>
			<view class="desc-txt">义务教育阳光招生入学服务平台</view>
			<image src="/static/people.png" class="desc-img"></image>
		</view>
		<view class="entrance-list">
			<template v-for="item, idx in entryList">
				<view class="entr-item enroll-item" @click="go2Ad(item.id)">{{ item.name }}</view>
			</template>
			<view class="entr-item enroll-item" @click="go2NewPage('apply-qry')">录取查询</view>
			<view class="entr-item" @click="go2NewPage('hotline')">咨询电话</view>
			<!-- <view class="entr-item" @click="go2NewPage('enroll-range')">招生范围</view> -->
			<view class="entr-item" @click="go2NewPage('policy')">政策公告</view>
		</view>
		<u-toast ref="entryTips"></u-toast>
	</view>
</template>

<script>
import { qryEntry, isInAdTimeRange1, getNextPath } from '@/apis/admission'
import { areaList } from '@/utils/dictionary.js'
export default {
	data() {
		return {
			curArea: '',
			entryList: [],
			entryNotAvail: false
		}
	},
	created() {
		this.curArea = areaList.find(v => v.id == this.$store.state.deptCode).val
		this.getEntrance()
		// 清空报名类型和学校
		this.$store.commit('SET_ENTRY1', '')
		this.$store.commit('SET_ENTRY2', '')
		this.$store.commit('SET_ENTRY3', '')
		this.$store.commit('SET_SCHOOL_DETAIL', {})
		this.$setCacheData4Ad({})
	},
  onShow() {
    if(this.$store.state.type === 8) {
      this.$store.commit('SET_DEPTCODE', 130525)
      this.curArea = areaList.find(v => v.id == this.$store.state.deptCode).val
      this.getEntrance()
      this.$store.commit('SET_ENTRY1', '')
      this.$store.commit('SET_ENTRY2', '')
      this.$store.commit('SET_ENTRY3', '')
      this.$store.commit('SET_SCHOOL_DETAIL', {})
      // this.$setCacheData4Ad({})
    }else {
      uni.clearStorage()
    }
  },
  methods: {
		// 获取报名入口
		getEntrance() {
			qryEntry({
				key: 1
			}).then(res => {
				this.entryList = res
			})
		},
		// 跳转其它
		go2NewPage(page) {
			uni.navigateTo({
				url: `/pages/${page}/index`
			})
		},
		// 报名入口
		async go2Ad(entryId) {
			let isStuAd = await getNextPath({ key: this.$store.state.userId })
			// 没报名的走时间验证
			if (isStuAd == 0) {
				isInAdTimeRange1().then(res => {
					if (entryId == 2) {
						if (res.villages) {
							this.go2Next(entryId)
						} else {
							this.$refs.entryTips.show({
								message: '当前入口不在报名时间内',
								type: 'error',
								duration: 3000
							})
						}
					} else if (entryId == 3) {
						if (res.urban) {
							this.go2Next(entryId)
						} else {
							this.$refs.entryTips.show({
								message: '当前入口不在报名时间内',
								type: 'error',
								duration: 3000
							})
						}
					}
				})
			} else if (isStuAd == 4) {
				// 驳回重报的不验证
				this.go2Next(entryId)
			} else {
				this.$refs.entryTips.show({
					message: '您已报名，修改信息请从录取查询处进入修改',
					type: 'warning',
					duration: 3000
				})
			}
		},
		// 跳转
		go2Next(entryId) {
			this.$store.commit('SET_ENTRY1', entryId)
			uni.navigateTo({
				url: `/pages/entry2/index`
			})
		},
		// 区县选择
		go2AreaSelect() {
			// 是微信才能切换区县
			if (this.$store.state.loginTp == 'wx') {
				this.$store.dispatch('logout').then(() => {
					location.href = `${ location.origin }/h5`
				})
			}
		}
	}
}
</script>

<style scoped lang="scss">
.banner {
	position: relative;
	display: flex;
	justify-content: center;
	align-items: flex-start;
	flex-direction: column;
	width: 100%;
	height: 202px;
	padding: 35px;
	padding-top: 0;
	color: #B3DDFD;
	box-sizing: border-box;
	z-index: 0;
	.cur-area {
		font-size: 32px;
		font-weight: bold;
		background: linear-gradient(180deg, #FFFFFF, #AEE9FF);
		-webkit-background-clip: text;
		color: transparent;
	}
	.banner-bg {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		z-index: -1;
	}
	.desc-img {
		position: absolute;
		top: 35%;
		right: 0;
		transform: translate(0, -50%);
		width: 137px;
		height: 103px;
		z-index: -1;
	}
}
.content {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.logo {
	height: 400rpx;
	width: 100%;
}

.entrance-list {
	position: relative;
	padding: 34px 0 20rpx;
	text-align: center;
	width: 100%;
	margin-top: -70px;
	background-color: #FFF;
	border-radius: 36px;
	z-index: 1;
}

.entrance-list .entr-item {
	width: calc(100% - 40px);
	margin: 0 auto;
	padding: 20px 0;
	margin-bottom: 15px;
	font-size: 14px;
	font-weight: bold;
	color: #000;
	background-color: #EEFAFF;
	box-shadow: 0 0 6px 3px rgba(0, 0, 0, 0.1);
}
.entrance-list .enroll-item {
	color: #FF8800;
	background-color: #FFFDEE;
}
</style>
