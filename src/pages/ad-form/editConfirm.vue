<template>
	<view class="edit-confirm">
		<text>确定修改当前报名信息吗？当前修改报名次数仅剩：<text style="color: #F56C6C;">{{ remain }}次</text></text>
		<view class="e-c-actions">
			<view class="btn-wrap">
				<u-button type="info" text="取消" @click="_goBack()"></u-button>
			</view>
			<view class="btn-wrap">
				<u-button type="primary" text="确定" @click="go2Edit"></u-button>
			</view>
		</view>
	</view>
</template>

<script>
import { adRemainEditCount } from '@/apis/admission'
export default {
	data() {
		return {
			stuIdCard: '',
			remain: '-'
		}
	},
	onLoad() {
		let that = this
		// #ifdef APP-NVUE
		const evtChanel = that.$scope.eventChannel
		// #endif
		// #ifndef APP-NVUE
		const evtChanel = that.getOpenerEventChannel()
		// #endif
		evtChanel.on('acceptDataFromOpenerPage', function (data) {
			that.stuIdCard = data.idCard
			that.getCount()
		})
	},
	methods: {
		// 获取剩余次数
		getCount() {
			adRemainEditCount({
				key: this.stuIdCard
			}).then(res => {
				this.remain = res
			})
		},
		// 跳转报名修改页
		go2Edit() {
			uni.navigateTo({
				url: './edit'
			})
		}
	}
}
</script>

<style scoped lang="scss">
.edit-confirm {
	height: 100vh;
	padding: 10px;
	box-sizing: border-box;
	background-color: #FFF;
	.e-c-actions {
		position: fixed;
		bottom: 20px;
		left: 0;
		width: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
		.btn-wrap {
			flex: 0 0 30%;
			margin: 0 5%;
		}
	}
}
</style>