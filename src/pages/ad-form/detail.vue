<template>
	<view class="ad-detail">
		<u-skeleton rows="4" :loading="loadingAdForm">
			<u-alert v-if="isWish" :title="`志愿学校：${ applyRes._enrollSchoolName.join(', ') }`" type="success"></u-alert>
			<u-alert v-else :title="`报名学校：${ adSchool }`" type="success"></u-alert>
				<view v-for="item, idx in normalForm" :key="idx" class="form-group">
					<u-alert :title="item.infoName" type="info"></u-alert>
					<view class="f-group-detail">
						<u-cell-group :border="false">
							<u-cell v-for="fi, fidx in item._normalItem" :key="fi.fieldId" :title="fi.fieldName">
								<view slot="value">
									<text v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4">{{ fi.fieldValue }}</text>
									<normal-select-ex :item-config.sync="fi" v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6"></normal-select-ex>
								</view>
							</u-cell>
						</u-cell-group>
						<u-cell-group :border="false">
							<u-cell v-for="fi, fidx in item._imgItem" :key="fi.fieldId" :title="fi.fieldName">
								<view slot="value">
									<normal-img-ex :item-config.sync="fi"></normal-img-ex>
								</view>
							</u-cell>
						</u-cell-group>
					</view>
				</view>
				<!-- 双胞胎 -->
				<view class="form-group" v-if="siblings.list.length > 0">
					<u-alert title="双胞胎" type="info"></u-alert>
					<view class="f-group-detail">
						<view class="sibs-list">
							<view class="sibs-item" v-for="si, sIdx in siblings.list" :key="sIdx">
								<view class="f-g-sub-title">双胞胎{{ sIdx + 1 }}</view>
								<u-cell-group :border="false">
									<u-cell v-for="fi, fidx in si._normalItem" :key="fi.fieldId" :title="fi.fieldName">
										<view slot="value">
											<text v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4">{{ fi.fieldValue }}</text>
											<normal-select-ex :item-config.sync="fi" v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6"></normal-select-ex>
										</view>
									</u-cell>
								</u-cell-group>
								<u-cell-group :border="false">
									<u-cell v-for="fi, fidx in si._imgItem" :key="fi.fieldId" :title="fi.fieldName">
										<view slot="value">
											<normal-img-ex :item-config.sync="fi"></normal-img-ex>
										</view>
									</u-cell>
								</u-cell-group>
							</view>
						</view>
					</view>
				</view>
				<!-- 房产 -->
				<view class="form-group" v-if="propertyForm.list.length > 0">
					<u-alert title="房产信息" type="info"></u-alert>
					<view class="f-group-detail">
						<template v-for="ppItem, ppIdx in propertyForm.list">
							<u-cell-group :border="false">
								<u-cell title="房产类型">
									<view slot="value">{{ propertyForm.tpCn }}</view>
								</u-cell>
								<u-cell v-for="fi, fidx in ppItem._normalItem" :key="fi.fieldId" :title="fi.fieldName">
									<view slot="value">
										<text v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4">{{ fi.fieldValue }}</text>
										<normal-select-ex :item-config.sync="fi" v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6"></normal-select-ex>
									</view>
								</u-cell>
							</u-cell-group>
							<u-cell-group :border="false">
								<u-cell v-for="fi, fidx in ppItem._imgItem" :key="fi.fieldId" :title="fi.fieldName">
									<view slot="value">
										<normal-img-ex :item-config.sync="fi"></normal-img-ex>
									</view>
								</u-cell>
							</u-cell-group>
						</template>
					</view>
				</view>
				<template v-if="others.list.length > 0">
					<view v-for="oItem, oIdx in others.list" :key="oItem.typeConfigId" class="form-group">
						<u-alert title="其他材料证明" type="info"></u-alert>
						<view class="f-group-detail">
							<u-cell-group :border="false">
								<u-cell v-for="fi, fidx in oItem._normalItem" :key="fi.fieldId" :title="fi.fieldName">
									<view slot="value">
										<text v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4">{{ fi.fieldValue }}</text>
										<normal-select-ex :item-config.sync="fi" v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6"></normal-select-ex>
									</view>
								</u-cell>
							</u-cell-group>
							<u-cell-group :border="false">
								<u-cell v-for="fi, fidx in oItem._imgItem" :key="fi.fieldId" :title="fi.fieldName">
									<view slot="value">
										<normal-img-ex :item-config.sync="fi"></normal-img-ex>
									</view>
								</u-cell>
							</u-cell-group>
						</view>
					</view>
				</template>
				<view class="btn-wrap">
					<u-button @click="_goBack()" type="info" text="返回"></u-button>
				</view>
				<template v-if="showModify">
					<view class="btn-wrap">
						<u-button type="primary" @click="go2EditConfirm">修改报名</u-button>
					</view>
					<view class="btn-wrap">
						<u-button type="error" @click="go2ResetConfirm">重新报名</u-button>
					</view>
				</template>
		</u-skeleton>
	</view>
</template>

<script>
import { adDetail, adRemainEditCount, isInAdTimeRange2, delAd } from '@/apis/admission'
import NormalSelectEx from "@/components/Exhibition/NormalSelectEx"
import NormalImgEx from "@/components/Exhibition/NormalImgEx"
import Timer from "@/components/timer.vue"
import { wishRequiredSetUpIds } from "@/utils/dictionary"
export default {
	components: {
		NormalSelectEx,
		NormalImgEx,
		Timer
	},
	data() {
		return {
			// 加载中
			loadingAdForm: true,
			// 录取查询来的报名信息
			applyRes: this.$store.state.applyQryResult,
			// 报名人
			stuId: '',
			// 学生身份证
			stuIdCard: '',
			adSchool: '',
			// 页面循环用list
			originList: [],
			// 非双胞胎非房产的普通字段
			normalForm: [],
			// 房产
			propertyForm: {
				// 当前选中类型名字
				tpCn: '',
				// 所有类型房产字段
				list: []
			},
			// 多胞胎
			siblings: {
				// 页面显示的form
				list: []
			},
			// 其他补充信息
			others: {
				list: []
			},
			// 是否显示修改报名
			showModify: false,
			// 是否志愿
			isWish: false
		}
	},
	created() {
		this.stuId = this.applyRes.studentId
		this.adSchool = this.applyRes.enrollSchoolName
		this.showModify = this.$store.state.showModifyTp == 2
		this.isWish = wishRequiredSetUpIds.indexOf(this.applyRes.registrationTypeId) != -1
		this.getData()
	},
	methods: {
		// 获取次数
		async getRemainCount() {
			// 剩余可修改次数
			let count = await adRemainEditCount({ key: this.stuIdCard })
			return count
		},
		// 获取表单
		getData() {
			adDetail({
				key: this.stuId
			}).then(res => {
				// 按typeConfigId从小到大排序
				let resCopy = JSON.parse(JSON.stringify(res)).sort((a, b) => a.typeConfigId - b.typeConfigId)
				// 分类
				this.originList = resCopy.map(this.separateImgAndNormal)
				// 取基础信息里的学生身份证
				this.stuIdCard = resCopy.find(v => v.typeConfigId == 1).leafFieldInfos.find(v => v.fieldId == 3).fieldValue
				// 非房产非双胞胎的普通字段，typeConfigId为1，2，4，5，6，7。直接添加验证规则
				this.normalForm = this.originList.filter(v => v.typeConfigId < 8 && v.typeConfigId != 3)
				// 房产字段：typeConfigId >= 8但小于19
				this.propertyForm.list = this.originList.filter(v => v.typeConfigId >= 8 &&  v.typeConfigId < 18)
				// 双胞胎字段：typeConfigId为3和19
				this.siblings.list = this.originList.filter(v => v.typeConfigId == 3 || v.typeConfigId == 19)
				// 如果有房产
				if (this.propertyForm.list.length > 0) {
					// 当前选中类型中文
					this.propertyForm.tpCn = this.propertyForm.list[0].infoName
				}
				// 其他补充信息
				this.others.list = this.originList.filter(v => v.typeConfigId == 18)
				this.loadingAdForm = false
			})
		},
		// 字段通用处理：区分图片与非图片字段
		separateImgAndNormal(item) {
			// 非图片字段
			item._normalItem = item.leafFieldInfos.filter(fi => fi.type == 1)
			// 图片字段
			item._imgItem = item.leafFieldInfos.filter(fi => fi.type == 2 && fi.fieldValue)
			return item
		},
		// 前往修改确认页
		go2EditConfirm() {
			let params = {
				idCard: this.stuIdCard
			}
			uni.navigateTo({
				url: './editConfirm',
				success (res) {
					res.eventChannel.emit('acceptDataFromOpenerPage', params)
				}
			})
		},
		// 前往重报确认页
		go2ResetConfirm() {
			let params = {
				idCard: this.stuIdCard
			}
			uni.navigateTo({
				url: './resetConfirm',
				success (res) {
					res.eventChannel.emit('acceptDataFromOpenerPage', params)
				}
			})
		},
	}
}
</script>

<style lang="scss" scoped>
.ad-detail {
	padding: 10px;
	.form-group	{
		margin-top: 10px;
	}
	.f-g-sub-title {
		margin-top: 10px;
		text-align: center;
		font-size: 13px;
		color: #CCC;
	}
	.btn-wrap {
		margin-bottom: 10px;
	}
}
</style>