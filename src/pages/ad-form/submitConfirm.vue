<template>
	<view class="submit-confirm">
		<view>1. 请您对所填报志愿顺序再次确认。</view>
		<view>2. 在规定报名时间内，每名学生有2次修改机会。</view>
		<view class="e-c-actions">
			<view class="btn-wrap">
				<u-button type="info" text="取消" @click="_goBack()"></u-button>
			</view>
			<view class="btn-wrap" v-if="pageTp == 'index'">
				<u-button type="primary" text="确定" @click="submitFromIndex" :disabled="submitDisable"></u-button>
			</view>
			<view class="btn-wrap" v-else-if="pageTp == 'edit'">
				<u-button type="primary" text="确定" @click="submitFromEdit" :disabled="submitDisable"></u-button>
			</view>
		</view>
		<u-modal :show="adSubmitModal.show" :title="adSubmitModal.title" :content='adSubmitModal.content' @confirm="back2Home"></u-modal>
	</view>
</template>

<script>
import { submitAd, submitAdSecond, getNextPath } from '@/apis/admission'
export default {
	data() {
		return {
			// 从哪个页面来的
			pageTp: 'index',
			// 传来的数据
			params: {},
			adSubmitModal: {
				show: false,
				title: '报名',
				content: ''
			},
			// 提交按钮loading
			submitDisable: false
		}
	},
	onLoad() {
		let that = this
		// #ifdef APP-NVUE
		const evtChanel = that.$scope.eventChannel
		// #endif
		// #ifndef APP-NVUE
		const evtChanel = that.getOpenerEventChannel()
		// #endif
		evtChanel.on('acceptDataFromOpenerPage', function (data) {
			that.pageTp = data.pageTp
			that.params = data.params
		})
	},
	methods: {
		// ad-form/index页的报名按钮
		async submitFromIndex() {
			this.submitDisable = true
			// 查询当前学生是否报过名
			let isStuAd = await getNextPath({ key: this.$store.state.userId })
			if (isStuAd == 0) {
				submitAd(this.params).then(res => {
					this.adSubmitModal.content = '报名成功'
					this.submitDisable = false
					this.adSubmitModal.show = true
				}).catch(err => {
					this.submitDisable = false
				})
			} else {
				submitAdSecond(this.params).then(res => {
					this.adSubmitModal.content = '修改报名成功'
					this.submitDisable = false
					this.adSubmitModal.show = true
				}).catch(err => {
					this.submitDisable = false
				})
			}
		},
		// ad-form/edit页的报名按钮
		submitFromEdit() {
			this.submitDisable = true
			submitAdSecond(this.params).then(res => {
				this.adSubmitModal.content = '修改报名成功'
				this.submitDisable = false
				this.adSubmitModal.show = true
			}).catch(err => {
				this.submitDisable = false
			})
		},
		// 提交成功，返回首页
		back2Home() {
			uni.reLaunch({
				url: '../index/index'
			})
		}
	}
}
</script>

<style scoped lang="scss">
.submit-confirm {
	height: 100vh;
	padding: 10px;
	box-sizing: border-box;
	background-color: #FFF;
	.e-c-actions {
		position: fixed;
		bottom: 20px;
		left: 0;
		width: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
		.btn-wrap {
			flex: 0 0 30%;
			margin: 0 5%;
		}
	}
}
</style>