<template>
	<view class="entrance">
		<u-alert v-if="!isWish" :title="`当前报名学校：${ adSchool }`" type="success" effect="dark"></u-alert>
		<u-alert title="报名类型" type="info" style="margin-top: 10px;"></u-alert>
		<view class="normal-select fixed-type en-tp">
			<u-action-sheet :actions="allEnrollTp" title="报名类型" :show="enrollAllTp.showSelect" @select="enrollTpChange" @close="closeSelector('enrollAllTp')" :closeOnClickOverlay="false"></u-action-sheet>
			<view class="ph-txt">报名类型</view>
			<view class="tp-select">
				<view class="fake-input" @click="openSelector('enrollAllTp')">
					<view class="res-txt">{{ enrollAllTp.tpCn }}</view>
					<text class="fake-input-icon">
						<u-icon name="arrow-down" color="#dadbde" size="14px"></u-icon>
					</text>
				</view>
				<view class="tp-actions">
					<u-button type="primary" :disabled="curEnrollTp == curPageEnrollTp" @click="openTpSwitchConfirm">确认修改</u-button>
				</view>
			</view>
		</view>
		<template v-if="isWish && !loadingAdForm">
			<u-alert title="志愿学校" type="info" style="margin-top: 10px;"></u-alert>
			<u-form :model="wish.form" ref="wish" labelPosition="top" errorType="toast" labelWidth="auto">
				<u-form-item prop="vol1" label="第一志愿">
					<view class="normal-select wish-tp">
						<u-action-sheet :actions="wish.allSchool" title="第一志愿" :show="wish.actualWishMap[0].show" @select="changeWish($event, 0)" @close="closeWishSelector(0)" :closeOnClickOverlay="false"></u-action-sheet>
						<view class="tp-select">
							<view class="fake-input" @click="openWishSelector(0)">
								<view class="res-txt" v-if="wish.form.vol1">{{ wish.actualWishMap[0].volunteerSchoolName }}</view>
								<view class="ph-txt" v-else>请选择第一志愿</view>
								<text class="fake-input-icon">
									<u-icon name="arrow-down" color="#dadbde" size="14px"></u-icon>
								</text>
							</view>
							<view class="tp-actions">
								<u-button type="primary" :disabled="!wish.form.vol1" @click="clearWish(0)">清空</u-button>
							</view>
						</view>
					</view>
				</u-form-item>
				<u-form-item prop="vol2" label="第二志愿">
					<view class="normal-select wish-tp">
						<u-action-sheet :actions="wish.allSchool" title="第二志愿" :show="wish.actualWishMap[1].show" @select="changeWish($event, 1)" @close="closeWishSelector(1)" :closeOnClickOverlay="false"></u-action-sheet>
						<view class="tp-select">
							<view class="fake-input" @click="openWishSelector(1)">
								<view class="res-txt" v-if="wish.form.vol2">{{ wish.actualWishMap[1].volunteerSchoolName }}</view>
								<view class="ph-txt" v-else>请选择第二志愿</view>
								<text class="fake-input-icon">
									<u-icon name="arrow-down" color="#dadbde" size="14px"></u-icon>
								</text>
							</view>
							<view class="tp-actions">
								<u-button type="primary" :disabled="!wish.form.vol2" @click="clearWish(1)">清空</u-button>
							</view>
						</view>
					</view>
				</u-form-item>
			<u-form-item prop="vol1" label="第三志愿">
					<view class="normal-select wish-tp">
						<u-action-sheet :actions="wish.allSchool" title="第三志愿" :show="wish.actualWishMap[2].show" @select="changeWish($event, 2)" @close="closeWishSelector(2)" :closeOnClickOverlay="false"></u-action-sheet>
						<view class="tp-select">
							<view class="fake-input" @click="openWishSelector(2)">
								<view class="res-txt" v-if="wish.form.vol3">{{ wish.actualWishMap[2].volunteerSchoolName }}</view>
								<view class="ph-txt" v-else>请选择第三志愿</view>
								<text class="fake-input-icon">
									<u-icon name="arrow-down" color="#dadbde" size="14px"></u-icon>
								</text>
							</view>
							<view class="tp-actions">
								<u-button type="primary" :disabled="!wish.form.vol3" @click="clearWish(2)">清空</u-button>
							</view>
						</view>
					</view>
				</u-form-item>
        <!--		<u-form-item prop="vol1" label="第四志愿">
            <view class="normal-select wish-tp">
              <u-action-sheet :actions="wish.allSchool" title="第四志愿" :show="wish.actualWishMap[3].show" @select="changeWish($event, 3)" @close="closeWishSelector(3)" :closeOnClickOverlay="false"></u-action-sheet>
              <view class="tp-select">
                <view class="fake-input" @click="openWishSelector(3)">
                  <view class="res-txt" v-if="wish.form.vol4">{{ wish.actualWishMap[3].volunteerSchoolName }}</view>
                  <view class="ph-txt" v-else>请选择第四志愿</view>
                  <text class="fake-input-icon">
                    <u-icon name="arrow-down" color="#dadbde" size="14px"></u-icon>
                  </text>
                </view>
                <view class="tp-actions">
                  <u-button type="primary" :disabled="!wish.form.vol4" @click="clearWish(3)">清空</u-button>
                </view>
              </view>
            </view>
          </u-form-item>
          <u-form-item prop="vol1" label="第五志愿">
            <view class="normal-select wish-tp">
              <u-action-sheet :actions="wish.allSchool" title="第五志愿" :show="wish.actualWishMap[4].show" @select="changeWish($event, 4)" @close="closeWishSelector(4)" :closeOnClickOverlay="false"></u-action-sheet>
              <view class="tp-select">
                <view class="fake-input" @click="openWishSelector(4)">
                  <view class="res-txt" v-if="wish.form.vol5">{{ wish.actualWishMap[4].volunteerSchoolName }}</view>
                  <view class="ph-txt" v-else>请选择第五志愿</view>
                  <text class="fake-input-icon">
                    <u-icon name="arrow-down" color="#dadbde" size="14px"></u-icon>
                  </text>
                </view>
                <view class="tp-actions">
                  <u-button type="primary" :disabled="!wish.form.vol5" @click="clearWish(4)">清空</u-button>
                </view>
              </view>
            </view>
          </u-form-item>-->
			</u-form>
		</template>
		<u-skeleton rows="10" :loading="loadingAdForm">
			<u-form :model="form" :rules="rules" ref="form" labelPosition="top" errorType="toast" labelWidth="auto">
				<!-- 非房产非双胞胎的普通字段 -->
				<view v-for="item, idx in normalForm" :key="idx" class="form-group">
					<u-alert :title="item.infoName" type="info"></u-alert>
					<view class="f-group-detail">
						<template v-for="fi, fidx in item._normalItem">
							<u-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" :required="fi.isNecessary == 1">
								<normal-input :ref="fi.fieldId" :item-config.sync="fi" @value-change="inputValueChange" v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4"></normal-input>
								<normal-select :ref="fi.fieldId" :item-config.sync="fi" @value-change="selectValueChange" v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6"></normal-select>
							</u-form-item>
						</template>
						<template v-for="fi, fidx in item._imgItem">
							<u-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" :required="fi.isNecessary == 1">
								<normal-img-upload :ref="fi.fieldId" :item-config.sync="fi" @value-change="uploadValueChange"></normal-img-upload>
							</u-form-item>
						</template>
					</view>
				</view>
				<!-- 双胞胎 -->
				<view class="form-group" v-if="siblings.allList.length > 0">
					<view class="fake-title">
						<text>双胞胎</text>
						<u-switch v-model="siblings.isHaveSib" @change="initOrHideSib"></u-switch>
					</view>
					<view class="f-group-detail" v-show="siblings.isHaveSib">
						<view class="sibs-list">
							<view class="sibs-item" v-for="si, sIdx in siblings.list" :key="sIdx">
								<view class="f-g-sub-title">双胞胎{{ sIdx + 1 }}</view>
								<template v-for="fi, fidx in si._normalItem">
									<u-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" :required="fi.isNecessary == 1">
										<normal-input :ref="fi.fieldId" :item-config.sync="fi" @value-change="inputValueChange" v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4"></normal-input>
										<normal-select :ref="fi.fieldId" :item-config.sync="fi" @value-change="selectValueChange" v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6"></normal-select>
									</u-form-item>
								</template>
								<template v-for="fi, fidx in si._imgItem">
									<u-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" :required="fi.isNecessary == 1">
										<normal-img-upload :ref="fi.fieldId" :item-config.sync="fi" @value-change="uploadValueChange"></normal-img-upload>
									</u-form-item>
								</template>
							</view>
						</view>
						<view class="sib-actions">
							<u-button type="primary" class="sib-actions-btn add" text="添加" @click="sibAdd" v-if="siblings.list.length == 1"></u-button>
							<u-button type="error" class="sib-actions-btn del" text="删除" @click="sibDel" v-if="siblings.list.length == 2"></u-button>
						</view>
					</view>
				</view>
				<!-- 随迁 -->
				<view class="form-group" v-if="sq.isSQ">
					<view class="fake-title">
						<text>{{ sq.title }}</text>
						<view class="sq-selector">
							<u-subsection @change="sqTpChange" :list="sq.selector" keyName="name" :current="sq.tpIdx"></u-subsection>
						</view>
					</view>
					<view class="f-group-detail">
						<view v-for="sqItem, sqIdx in sq.list" :key="sqItem.typeConfigId">
							<view v-for="fi, fidx in sqItem._normalItem" :key="fi.fieldId">
								<u-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" :required="fi.isNecessary == 1">
									<normal-input :ref="fi.fieldId" :item-config.sync="fi" @value-change="inputValueChange" v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4"></normal-input>
									<normal-select :ref="fi.fieldId" :item-config.sync="fi" @value-change="selectValueChange" v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6"></normal-select>
								</u-form-item>
							</view>
							<view v-for="fi, fidx in sqItem._imgItem" :key="fi.fieldId">
								<u-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" :required="fi.isNecessary == 1">
									<normal-img-upload :ref="fi.fieldId" :item-config.sync="fi" @value-change="uploadValueChange"></normal-img-upload>
								</u-form-item>
							</view>
						</view>
					</view>
				</view>
				<!-- 房产 -->
				<view class="form-group" v-if="propertyForm.list.length > 0">
					<u-alert title="房产信息" type="info"></u-alert>
					<view class="normal-select fixed-type" @click="openSelector('propertyForm')">
						<u-action-sheet :actions="propertyForm.selector" title="房产类型" :show="propertyForm.showSelect" @select="ppTabChange" @close="closeSelector('propertyForm')" :closeOnClickOverlay="false"></u-action-sheet>
						<view class="ph-txt"><text class="pp-tp-required">*</text>房产类型</view>
						<view class="fake-input">
							<view class="res-txt">{{ propertyForm.tpCn }}</view>
							<text class="fake-input-icon">
								<u-icon name="arrow-down" color="#dadbde" size="14px"></u-icon>
							</text>
						</view>
					</view>
					<view class="f-group-detail">
						<view v-for="ppItem, ppIdx in propertyForm.curList" :key="ppItem.typeConfigId">
							<view v-for="fi, fidx in ppItem._normalItem" :key="fi.fieldId">
								<u-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" :required="fi.isNecessary == 1">
									<normal-input :ref="fi.fieldId" :item-config.sync="fi" @value-change="inputValueChange" v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4"></normal-input>
									<normal-select :ref="fi.fieldId" :item-config.sync="fi" @value-change="selectValueChange" v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6"></normal-select>
								</u-form-item>
							</view>
							<view v-for="fi, fidx in ppItem._imgItem" :key="fi.fieldId">
								<u-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" :required="fi.isNecessary == 1">
									<normal-img-upload :ref="fi.fieldId" :item-config.sync="fi" @value-change="uploadValueChange"></normal-img-upload>
								</u-form-item>
							</view>
              <div class="f-group-tips">
                <div class="tips-item" v-if="[17].includes(Number(ppItem.typeConfigId))">
                  <p>注：房产证、不动产证、购房合同均可</p>
                </div>
              </div>
						</view>
					</view>
				</view>
				<view class="form-group" v-if="others.list.length > 0">
					<u-alert title="其他材料证明" type="info"></u-alert>
					<view class="f-group-detail">
						<view v-for="oItem, oIdx in others.list" :key="oItem.typeConfigId">
							<template v-for="fi, fidx in oItem._normalItem">
								<u-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" :required="fi.isNecessary == 1">
									<normal-input :ref="fi.fieldId" :item-config.sync="fi" @value-change="inputValueChange" v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4"></normal-input>
									<normal-select :ref="fi.fieldId" :item-config.sync="fi" @value-change="selectValueChange" v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6"></normal-select>
								</u-form-item>
							</template>
							<template v-for="fi, fidx in oItem._imgItem">
								<u-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" :required="fi.isNecessary == 1">
									<normal-img-upload :ref="fi.fieldId" :item-config.sync="fi" @value-change="uploadValueChange"></normal-img-upload>
								</u-form-item>
							</template>
						</view>
					</view>
				</view>
			</u-form>
      <view class="submit-btn">
        <u-button @click="submitAdForm" :disabled="submitDisable">提交</u-button>
      </view>
		</u-skeleton>
		<u-modal :showCancelButton="true" @cancel="closeTpSwitchConfirm" :show="enrollTpSwitchConfirm.show" :title="enrollTpSwitchConfirm.title" :content='enrollTpSwitchConfirm.content' @confirm="confirmEditEnTp" ></u-modal>
		<u-toast ref="uToast"></u-toast>
	</view>
</template>

<script>
import { qryEditAdForm, submitAdSecond, getAllAdTp, schoolList } from '@/apis/admission'
import { wishRequiredSetUpIds } from "@/utils/dictionary"
import NormalInput from "@/components/Form/NormalInput"
import NormalSelect from "@/components/Form/NormalSelect"
import NormalImgUpload from "@/components/Form/NormalImgUpload"
import { LoopFn } from "@/mixins/loopFn"
export default {
	components: { 
		NormalInput,
		NormalSelect,
		NormalImgUpload
	},
	mixins: [ LoopFn ],
	data() {
		return {
			// 所有报名类型
			allEnrollTp: [],
			// 当前报名类型, 选择框的值
			curEnrollTp: '',
			// 页面展示的报名类型,
			// 切换了新报名类型，填写完信息再去修改curEnrollTp会导致提交类型与页面展示类型不一致
			curPageEnrollTp: '',
			// 录取查询来的报名信息
			applyRes: this.$store.state.applyQryResult,
			adSchool: '',
			enrollAllTp: {
				showSelect: false,
				tpCn: '--'
			},
			enrollTpSwitchConfirm: {
				show: false,
				title: '提示',
				content: '部分已填信息将被清空，确定修改报名类型？'
			},
			numberCn: ['一', '二', '三', '四', '五', '六'],
			wish: {
				// 页面展示表单
				form: {
					vol1: '',
					vol2: '',
					vol3: '',
          /* vol4: '',
          vol5: ''*/
				},
				// 实际后端需要的结构
				actualWishMap: [{
					severalVolunteer: 1,
					volunteerSchoolId: '',
					volunteerSchoolName: '',
					show: false
				}, {
					severalVolunteer: 2,
					volunteerSchoolId: '',
					volunteerSchoolName: '',
					show: false
				}
          , {
            severalVolunteer: 3,
            volunteerSchoolId: '',
            volunteerSchoolName: '',
            show: false
          }
          /*, {
            severalVolunteer: 4,
            volunteerSchoolId: '',
            volunteerSchoolName: '',
            show: false
          }, {
            severalVolunteer: 5,
            volunteerSchoolId: '',
            volunteerSchoolName: '',
            show: false
          }*/
        ],
				allSchool: [],
				show: false
			}
		}
	},
	created() {
		this.adSchool = this.applyRes.enrollSchoolName
		this.curEnrollTp = this.applyRes.registrationTypeId
		this.curPageEnrollTp = this.applyRes.registrationTypeId
		// 是否志愿报名
		this.isWish = wishRequiredSetUpIds.indexOf(this.curEnrollTp) != -1
		if (this.isWish) {
			this.getWishSchool()
		}
		// 是否随迁类型
		this.sq.isSQ = this.sq.idList.some(v => v == this.curEnrollTp)
		this.getData()
		this.getAllEnrollTp()
	},
	methods: {
		// 开启志愿选择
		openWishSelector(idx) {
			this.wish.actualWishMap[idx].show = true
		},
		// 关闭志愿选择
		closeWishSelector(idx) {
			this.wish.actualWishMap[idx].show = false
		},
		// 志愿时获取学校
		getWishSchool() {
			schoolList({
			  nature: this.applyRes.applyType - 1,
				deptCode: this.$store.state.deptCode,
			  period: 3,
				type: 1,
				pageNumber: 1,
				pageSize: 99,
				keywords: "",
				rejectSchoolId: ''
			}).then(res => {
				res.records.forEach(v => {
					v.name = v.deptName
				})
				this.wish.allSchool = res.records
			})
		},
		// 清空志愿
		clearWish(idx) {
			this.wish.form[`vol${ idx + 1 }`] = ''
			this.wish.actualWishMap[idx].volunteerSchoolId = ''
			this.wish.actualWishMap[idx].volunteerSchoolName = ''
		},
		// 修改志愿
		changeWish(evt, idx) {
			let curWish = evt.id
			// 先赋值
			this.wish.form[`vol${ idx + 1 }`] = curWish
			this.wish.actualWishMap[idx].volunteerSchoolId = curWish
			this.wish.actualWishMap[idx].volunteerSchoolName = this.wish.allSchool.find(v => v.id == curWish).deptName
			let allWish = Object.values(this.wish.form)
			let isConflict = allWish.filter(v => v && v == curWish)
			// 同一个学校: id为1810512784115929090和1810512958510895105
			let sameSchool = ['1810512784115929090', '1810512958510895105']
			// 检查是否同一个学校
			let isSameSchool = allWish.filter(v => sameSchool.indexOf(v) != -1)
			// 重复时再清空
			if (isConflict.length > 1) {
				this.wish.form[`vol${ idx + 1 }`] = ''
				this.wish.actualWishMap[idx].volunteerSchoolId = ''
				this.wish.actualWishMap[idx].volunteerSchoolName = ''
				this.$refs.uToast.show({
					type: 'error',
					message: '志愿学校不能重复'
				})
			} else if (isSameSchool.length > 1) {
				this.wish.form[`vol${ idx + 1 }`] = ''
				this.wish.actualWishMap[idx].volunteerSchoolId = ''
				this.wish.actualWishMap[idx].volunteerSchoolName = ''
				this.$refs.uToast.show({
					type: 'error',
					message: '走读学校和寄宿学校只能二选一'
				})
			}
		},
		// 根据学校和当前报名类型查询可用报名类型
		getAllEnrollTp() {
			getAllAdTp({
				setUpId: this.curEnrollTp,
				schoolId: this.applyRes.enrollSchoolId
			}).then(res => {
				let list = res.map(v => {
					return {
						name: v.idsName,
						id: v.setupId
					}
				})
				this.allEnrollTp = list
				// 如果修改时报名入口不存在了，将默认选中的入口置空
				if (res.every(v => v.setupId != this.curEnrollTp)) {
					this.enrollTpChange({ id: '', name: '请选择报名类型' })
				} else {
					this.enrollAllTp.tpCn = list.find(v => v.id == this.curEnrollTp).name
				}
			})
		},
		// 报名类型修改
		enrollTpChange(item) {
			this.curEnrollTp = item.id
			this.enrollAllTp.tpCn = item.name
		},
		// 打开报名类型确定
		openTpSwitchConfirm() {
			this.enrollTpSwitchConfirm.show = true
		},
		// 关闭报名类型确定
		closeTpSwitchConfirm() {
			this.enrollTpSwitchConfirm.show = false
		},
		// 确定修改报名类型
		confirmEditEnTp() {
			// 重置房产
			this.propertyForm.tp = 0
			this.propertyForm.lastTp = 0
			this.propertyForm.tpCn = 0
			this.propertyForm.list = []
			this.propertyForm.curList = []
			this.propertyForm.selector = []
			// 重置双胞胎
			this.siblings.allList = []
			this.siblings.list = []
			this.siblings.isHaveSib = false
			// 重置其它
			this.others.list = []
			// 重置随迁
			this.sq.selector = []
			this.sq.isSQ = this.sq.idList.some(v => v == this.curEnrollTp)
			this.sq.lastTp = ''
			this.sq.tp = ''
			this.sq.tpIdx = ''
			this.sq.list = []
			this.sq.allList = []
			this.loadingAdForm = true
			this.closeTpSwitchConfirm()
			this.getData()
		},
		// 获取表单
		getData() {
			qryEditAdForm({
				setupId: this.curEnrollTp,
				studentId: this.applyRes.studentId
			}).then(res => {
				// 按typeConfigId从小到大排序
				let resCopy = JSON.parse(JSON.stringify(res.enrollMiddleFieldVoList)).sort((a, b) => a.typeConfigId - b.typeConfigId)
				this.curPageEnrollTp = this.curEnrollTp
				// 志愿们
				if (this.isWish) {
					let wishResult = res.studentVolunteerVoList.sort((a, b) => a.severalVolunteer - b.severalVolunteer)
					this.wish.actualWishMap = wishResult.map(v => {
						return {
							...v,
							show: false
						}
					})
					// 循环赋值
					wishResult.forEach((v, i) => {
						this.wish.form[`vol${i + 1}`] = v.volunteerSchoolId
					})
				}
				// 分类
				this.originList = resCopy.map(this.separateImgAndNormal)
				// 是随迁
				if (this.sq.isSQ) {
					// 排除经商5和务工6
					let normalIdList = [1, 2, 4, 7].map(v => `${ v }`)
					this.normalForm = this.originList.filter(v => normalIdList.indexOf(v.typeConfigId) != -1).map(this.addValidRules)
					this.sq.allList = this.originList.filter(v => v.typeConfigId == 5 || v.typeConfigId == 6)
					// 如果配置了经商务工
					if (this.sq.allList.length > 0) {
						this.sq.selector = this.sq.allList.map(v => {
							return {
								name: v.infoName,
								id: v.typeConfigId
							}
						})
						let selected = ''
						// 其它不包含经商务工的报名类型修改为随迁，followWorkType为0
						if (res.followWorkType == 0) {
							// 那就默认选中第1个
							selected = this.sq.allList[0].typeConfigId
						} else {
							// 否则直接取
							selected = res.followWorkType
						}
						this.sq.tp = this.sq.selector.findIndex(v => v.id == selected)
						this.sqTpChange(this.sq.tp)
					}
				} else {
					let normalIdList = [1, 2, 4, 5, 6, 7].map(v => `${ v }`)
					this.normalForm = this.originList.filter(v => normalIdList.indexOf(v.typeConfigId) != -1).map(this.addValidRules)
				}
				// 房产字段：typeConfigId >= 8但小于19
				this.propertyForm.list = this.originList.filter(v => v.typeConfigId >= 8 &&  v.typeConfigId < 18)
				// 双胞胎字段：typeConfigId为3和19
				this.siblings.allList = this.originList.filter(v => v.typeConfigId == 3 || v.typeConfigId == 19)
				if (this.siblings.allList.length > 0) {
					this.siblings.list = JSON.parse(JSON.stringify(this.siblings.allList))
					// 是否打开父级开关
					this.siblings.isHaveSib = this.siblings.allList.some(v => v.leafFieldInfos.some(v1 => v1.fieldValue))
					// 打开了
					if (this.siblings.isHaveSib) {
						// 是否打开第2个多胞胎
						let isOpenSecondSib = this.siblings.allList.filter(v => v.typeConfigId == 19).some(v => v.leafFieldInfos.some(v1 => v1.fieldValue))
						if (!isOpenSecondSib) {
							this.sibDel()
						}
					}
				}
				// 如果有房产
				if (this.propertyForm.list.length > 0) {
					// 初始化房产选择器
					this.propertyForm.selector = this.propertyForm.list.map(v => {
						return {
							name: v.infoName,
							id: v.typeConfigId
						}
					})
					// 如果上个报名类型没有房产字段
					if (res.houseInfoType == 0) {
						// // 选中第1个房产类型
						// // this.propertyForm.list[0].typeConfigId
						// this.propertyForm.tp = ''
            const firstItem = this.propertyForm.list.find(() => true);
            this.propertyForm.tp = firstItem ? firstItem.typeConfigId : '';
					} else {
						this.propertyForm.tp = `${ res.houseInfoType }`
					}
					this.propertyForm.lastTp = this.propertyForm.tp
					// 当前选中类型中文
					this.propertyForm.tpCn = this.propertyForm.selector.find(v => v.id == this.propertyForm.tp).name
					// 当前选中类型表单
					this.propertyForm.curList = this.propertyForm.list.filter(v => v.typeConfigId == this.propertyForm.tp)
					// 添加第1个tab的验证规则
					this.propertyForm.list.filter(v => v.typeConfigId == this.propertyForm.tp).map(this.addValidRules)
				}
				// 其他补充信息
				this.others.list = this.originList.filter(v => v.typeConfigId == 18)
				if (this.others.list.length > 0) {
					this.others.list.map(this.addValidRules)
				}
				this.loadingAdForm = false
			})
		},
		// 提交
		submitAdForm() {
			this.submitDisable = true
			let params = {
				setUpSaveIds: this.curPageEnrollTp,
				enrollSchoolId: this.applyRes.enrollSchoolId,
				enrollSchoolName: this.applyRes.enrollSchoolName,
				userId: this.$store.state.userInfo.id,
				enrollMiddleFieldFormList: this.originList,
				houseType: this.propertyForm.tp,
				followWorkType: ''
			}
			// 先检查是否志愿报名
			if (this.isWish) {
				// 所有未选择志愿的行数
				let emptyWish = []
				this.wish.actualWishMap.forEach((v, i) => {
					if (!v.volunteerSchoolName) {
						emptyWish.push(this.numberCn[i])
					}
				})
				if (emptyWish.length > 0) {
					this.$refs.uToast.show({
						type: 'error',
						message: `请选择第${ emptyWish.join('、') }志愿`
					})
					this.submitDisable = false
				} else {
					// 带上志愿
					params.studentVolunteerFormList = this.wish.actualWishMap
					// 学校为第一个志愿学校
					params.enrollSchoolId = this.wish.actualWishMap[0].volunteerSchoolId
					params.enrollSchoolName = this.wish.actualWishMap[0].volunteerSchoolName
				}
			}
			// 没选双胞胎就去掉上传字段
			if (!this.siblings.isHaveSib) {
				params.enrollMiddleFieldFormList = params.enrollMiddleFieldFormList.filter(v => v.typeConfigId != 3 && v.typeConfigId != 19)
			} else if (this.siblings.list.length == 1) {
				// 只有一条时删掉id是19的
				params.enrollMiddleFieldFormList = params.enrollMiddleFieldFormList.filter(v => v.typeConfigId != 19)
			}
			// 是随迁传入选中的随迁类型
			if (this.sq.isSQ) {
				params.followWorkType = this.sq.tp
			}
			this.$refs['form'].validate().then(valid => {
				if (valid) {
					this.submitDisable = false
					uni.navigateTo({
						url: './submitConfirm',
						success (res) {
							res.eventChannel.emit('acceptDataFromOpenerPage', {
								pageTp: 'edit',
								params
							})
						}
					})
				}
			}).catch(err => {
				this.submitDisable = false
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.entrance {
	padding: 10px;
	.fake-title {
		position: relative;
		padding: 8px 10px;
		display: flex;
		justify-content: space-between;
		align-items: center;
		font-size: 14px;
		font-weight: bold;
		color: #909399;
		border-radius: 4px;
		background-color: #f4F4F5;
	}
	.sq-selector {
		width: 50%;
	}
	.sibs-item {
		padding: 10px;
		border-bottom: 1px dashed #dcdfe6;
	}
	.sib-actions {
		margin: 10px 0;
		.sib-actions-btn {
			width: 30%;
		}
	}
	.f-g-sub-title {
		font-style: italic;
		color: #CCC;
	}
	.fixed-type {
		margin-bottom: 10px;
		.ph-txt {
			margin-top: 15px;
			margin-bottom: 5px;
			font-size: 15px;
		}
	}
	.en-tp {
		.tp-select {
			display: flex;
			justify-content: space-between;
			align-items: center;
		}
		.fake-input {
			flex: 0 0 68%;
		}
		.tp-actions {
			flex: 0 0 30%;
		}
	}
	.wish-tp {
		.tp-select {
			display: flex;
			justify-content: space-between;
			align-items: center;
		}
		.fake-input {
			flex: 0 0 80%;
		}
		.tp-actions {
			flex: 0 0 18%;
		}
	}
	.pp-tp-required {
		position: relative;
		top: 5px;
		padding-right: 3px;
		color: #f56c6c;
		font-size: 20px;
	}
}
.tips-item {
  color: red; /* 设置字体为红色 */
  font-size: 13px; /* 设置字体大小 */
}
</style>