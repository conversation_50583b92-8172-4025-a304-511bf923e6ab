<template>
	<view class="reset-confirm">
		<text>确定删除学生报名数据吗？删除以后请尽快在报名时期间重新报名，否则该学生可能导致无法正常入学，当前修改报名次数仅剩：<text style="color: #F56C6C;">{{ remain }}次</text>，操作不可逆，请谨慎操作。</text>
		<view class="r-c-actions">
			<view class="btn-wrap">
				<u-button type="info" text="取消" @click="_goBack()"></u-button>
			</view>
			<view class="btn-wrap">
				<u-button type="primary" :disabled="confirmBtnDisable" :text="confirmBtnTxt" @click="delAndResetAd"></u-button>
			</view>
		</view>
		<timer ref="resetAdTimer" :total="timerTotalSec" @start="timerStart" @ongoing="timerOngoing" @end="timerEnd"></timer>
		<u-modal :show="delSuccess.show" :title="delSuccess.title" :content='delSuccess.content' @confirm="back2Home"></u-modal>
	</view>
</template>

<script>
import { adRemainEditCount, delAd } from '@/apis/admission'
import Timer from "@/components/timer.vue"
export default {
	components: {
		Timer
	},
	data() {
		return {
			stuIdCard: '',
			remain: '-',
			// 倒计时时长，单位秒
			timerTotalSec: 3,
			// 默认禁用确定按钮
			confirmBtnDisable: true,
			// 确定按钮txt
			confirmBtnTxt: '',
			delSuccess: {
				show: false,
				title: '删除成功',
				content: '报名信息已删除，请重新报名'
			}
		}
	},
	onLoad() {
		let that = this
		// #ifdef APP-NVUE
		const evtChanel = that.$scope.eventChannel
		// #endif
		// #ifndef APP-NVUE
		const evtChanel = that.getOpenerEventChannel()
		// #endif
		evtChanel.on('acceptDataFromOpenerPage', function (data) {
			that.stuIdCard = data.idCard
			that.getCount()
		})
	},
	onReady() {
		this.$refs['resetAdTimer']._startTimer()
	},
	onUnload() {
		this.$refs['resetAdTimer']._endTimer()
	},
	methods: {
		// 获取剩余次数
		getCount() {
			adRemainEditCount({
				key: this.stuIdCard
			}).then(res => {
				this.remain = res
			})
		},
		// 确定删除报名信息
		delAndResetAd() {
			delAd({
				key: this.$store.state.userId
			}).then(res => {
				this.delSuccess.show = true
			})
		},
		// 提交成功，返回首页
		back2Home() {
			uni.reLaunch({
				url: '../index/index'
			})
		},
		// 倒计时开始
		timerStart() {
			this.confirmBtnTxt = `确定（${ this.timerTotalSec }秒）`
		},
		// 倒计时进行中
		timerOngoing(remain) {
			this.confirmBtnTxt = `确定（${ remain }秒）`
		},
		// 倒计时结束
		timerEnd() {
			this.confirmBtnTxt = `确定`
			this.confirmBtnDisable = false
		},
	}
}
</script>

<style scoped lang="scss">
.reset-confirm {
	height: 100vh;
	padding: 10px;
	box-sizing: border-box;
	background-color: #FFF;
	.r-c-actions {
		position: fixed;
		bottom: 20px;
		left: 0;
		width: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
		.btn-wrap {
			flex: 0 0 30%;
			margin: 0 5%;
		}
	}
}
</style>