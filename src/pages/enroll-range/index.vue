<template>
	<view class="enroll-range">
		<u-alert title="小学招生范围" type="warning"></u-alert>
		<view class="e-r-content">
			<u-parse :content="cont1"></u-parse>
		</view>
		<u-alert title="初中招生范围" type="warning"></u-alert>
		<view class="e-r-content">
			<u-parse :content="cont2"></u-parse>
		</view>
	</view>
</template>

<script>
import { policyDetail } from '@/apis/other'
export default {
	data() {
		return {
			cont1: '',
			cont2: ''
		}
	},
	created() {
		this.getData(1)
		this.getData(2)
	},
	methods: {
		// 详情
		getData(id) {
			policyDetail({
				key: id
			}).then(res => {
				this[`cont${ id }`] = res.content
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.enroll-range {
	padding: 10px;
		.e-r-content {
			padding: 10px;
		}
}
</style>