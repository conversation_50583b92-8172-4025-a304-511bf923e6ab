<template>
	<view></view>
</template>

<script>
import userApis from '@/apis/modules/user'
export default {
	data() {
		return {
			token: '',
			// 是否冀时办登录
			isJsb: false
		}
	},
	created() {
		uni.showLoading({
			title: '正在登录，请稍候...',
			mask: true
		})
		let tokenStartIdx = location.href.indexOf('token=')
		this.token = location.href.substring(tokenStartIdx).replace('token=', '')
		// 是否冀时办登录
		this.isJsb = location.href.indexOf('type=jsb') > -1
		this.login()
	},
	methods: {
		// 登录
		login() {
			if (this.isJsb) {
				userApis.jsbLogin(this.token).then(data => {
					this.$store.commit('setLoginTp', 'jsb')
					this.afterLogin(data)
				}).catch(err => {
					uni.hideLoading()
					uni.showModal({
						content: '登录失败，请返回冀时办app重新点击报名',
						showCancel: false
					})
				})
			} else {
				userApis.provPltFmLogin(this.token).then(data => {
					this.$store.commit('setLoginTp', 'prov')
					this.afterLogin(data)
				}).catch(err => {
					uni.hideLoading()
					uni.showModal({
						content: '登录失败，请返回冀时办app重新点击报名',
						showCancel: false
					})
				})
			}
		},
		// 登录后操作
		afterLogin(data) {
			this.$store.commit('SET_USER_ID', data.id)
			this.$store.commit('SET_TOKEN', data.token)
			this.$store.commit('SET_USERINFO', data)
			this.$store.commit('SET_ROLE', data.roleCode)
			this.$store.commit('SET_ISFIVE', data.isFive)
			this.$store.commit('SET_ISBIND', data.binding)
			this.$store.commit('SET_ISDEFAULTPASSWORD', data.defaultPasswordFlag)
			this.$store.commit('SET_STATE4_HEADERS', data.state)
			this.$store.commit('SET_HOST', location.origin)
			uni.hideLoading()
			uni.showLoading({
				title: '登录成功',
				mask: true
			})
			uni.redirectTo({
				url: '/pages/index/index'
			})
		}
	}
}
</script>
