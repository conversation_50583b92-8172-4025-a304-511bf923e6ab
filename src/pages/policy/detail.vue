<template>
	<view class="policy-detail">
		<view class="policy-h">{{ title }}</view>
		<u-parse :content="content" @imgTap="seeImg" :previewImg="false"></u-parse>
	</view>
</template>

<script>
import { policyDetail } from '@/apis/other'
export default {
	data() {
		return {
			id: '',
			title: '',
			content: ''
		}
	},
	onLoad() {
		let that = this
		// #ifdef APP-NVUE
		const evtChanel = that.$scope.eventChannel
		// #endif
		// #ifndef APP-NVUE
		const evtChanel = that.getOpenerEventChannel()
		// #endif
		evtChanel.on('acceptDataFromOpenerPage', function (data) {
			that.id = data.id
			that.getData()
		})
	},
	methods: {
		getData() {
			policyDetail({
				key: this.id
			}).then(res => {
				this.title = res.title
				this.content = res.content
			})
		},
		// 图片点击
		seeImg(cfg) {
			uni.previewImage({
				urls: [`${ location.origin }/${cfg.src}`],
				current: 0
			})
		}
	}
}
</script>

<style scoped lang="scss">
.policy-detail {
	padding: 15px;
	padding-top: 0;
	.policy-h {
		font-size: 18px;
		margin-bottom: 10px;
		text-align: center;
	}
}
</style>